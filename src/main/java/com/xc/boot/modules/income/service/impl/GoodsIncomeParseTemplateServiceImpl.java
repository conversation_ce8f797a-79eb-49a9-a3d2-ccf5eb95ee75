package com.xc.boot.modules.income.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.base.CustomColumnItemDTO;

import com.xc.boot.common.util.CommonUtils;

import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.income.model.dto.GoodsIncomeTemplateExportDTO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailVO;
import com.xc.boot.modules.income.service.GoodsIncomeParseTemplateService;
import com.xc.boot.modules.merchant.mapper.*;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateMapper;
import com.xc.boot.modules.merchant.model.entity.*;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.enums.CounterTypeEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnCategoryEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.system.model.vo.HeadingColumns;
import com.xc.boot.system.model.vo.TableHeading;
import com.xc.boot.system.service.HeadingSignsService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 入库单模板服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsIncomeParseTemplateServiceImpl implements GoodsIncomeParseTemplateService {

    /**
     * 入库单模板详情Mapper
     */
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;

    /**
     * 入库单模板Mapper
     */
    private final GoodsIncomeTemplateMapper goodsIncomeTemplateMapper;

    /**
     * 商品字段Mapper
     */
    private final GoodsColumnMapper goodsColumnMapper;

    /**
     * 柜台Mapper
     */
    private final CounterMapper counterMapper;

    /**
     * 子类Mapper
     */
    private final SubclassMapper subclassMapper;

    /**
     * 品牌Mapper
     */
    private final BrandMapper brandMapper;

    /**
     * 款式Mapper
     */
    private final StyleMapper styleMapper;

    /**
     * 成色Mapper
     */
    private final QualityMapper qualityMapper;

    /**
     * 工艺Mapper
     */
    private final TechnologyMapper technologyMapper;

    /**
     * 珠石Mapper
     */
    private final JewelryMapper jewelryMapper;

    /**
     * 货品Mapper
     */
    private final GoodsMapper goodsMapper;

    /**
     * 表头配置服务
     */
    private final HeadingSignsService headingSignsService;

    /**
     * 关联数据映射
     * 用于存储各种关联数据的名称到ID的映射关系
     */
    private static class AssociationMapping {
        /**
         * 柜台名称到ID的映射
         */
        private final Map<String, Long> counterMap = new HashMap<>();

        /**
         * 子类名称到ID的映射
         */
        private final Map<String, Long> subclassMap = new HashMap<>();

        /**
         * 品牌名称到ID的映射
         */
        private final Map<String, Long> brandMap = new HashMap<>();

        /**
         * 款式名称到ID的映射
         */
        private final Map<String, Long> styleMap = new HashMap<>();

        /**
         * 成色名称到ID的映射
         */
        private final Map<String, Long> qualityMap = new HashMap<>();

        /**
         * 工艺名称到ID的映射
         */
        private final Map<String, Long> technologyMap = new HashMap<>();

        /**
         * 珠石名称到ID的映射
         */
        private final Map<String, Long> jewelryMap = new HashMap<>();

        /**
         * 自定义字段映射
         * key: 字段标识
         * value: 字段信息
         */
        private final Map<String, GoodsColumnEntity> customColumnMap = new HashMap<>();



        /**
         * 成色与大类的关联映射
         * key: 成色ID
         * value: 大类ID
         */
        private final Map<Long, Integer> qualityCategoryMap = new HashMap<>();

        /**
         * 柜台与门店的关联映射
         * key: 柜台ID
         * value: 门店ID
         */
        private final Map<Long, Integer> counterMerchantMap = new HashMap<>();
    }

    /**
     * 关联数据映射实例
     */
    private AssociationMapping associationMapping;

    /**
     * 初始化关联数据映射
     */
    private void initAssociationMappings() {
        associationMapping = new AssociationMapping();
        
        // 获取所有关联数据
        List<CounterEntity> counters = counterMapper.selectListByQuery(
            QueryWrapper.create()
                .where(CounterEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(CounterEntity::getType).ne(CounterTypeEnum.OLD_MATERIAL.getValue()) // 排除旧料
        );
        List<SubclassEntity> subclasses = subclassMapper.selectListByQuery(
            QueryWrapper.create()
                .where(SubclassEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<BrandEntity> brands = brandMapper.selectListByQuery(
            QueryWrapper.create()
                .where(BrandEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<StyleEntity> styles = styleMapper.selectListByQuery(
            QueryWrapper.create()
                .where(StyleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<QualityEntity> qualities = qualityMapper.selectListByQuery(
            QueryWrapper.create()
                .where(QualityEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<TechnologyEntity> technologies = technologyMapper.selectListByQuery(
            QueryWrapper.create()
                .where(TechnologyEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<JewelryEntity> jewelries = jewelryMapper.selectListByQuery(
            QueryWrapper.create()
                .where(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        // 构建映射
        counters.forEach(counter -> {
            associationMapping.counterMap.put(counter.getName(), counter.getId());
            // 构建柜台与门店的关联映射
            associationMapping.counterMerchantMap.put(counter.getId(), counter.getMerchantId());
        });
        subclasses.forEach(subclass -> {
            associationMapping.subclassMap.put(subclass.getName(), subclass.getId());
        });
        brands.forEach(brand -> associationMapping.brandMap.put(brand.getName(), brand.getId()));
        styles.forEach(style -> associationMapping.styleMap.put(style.getName(), style.getId()));
        qualities.forEach(quality -> {
            associationMapping.qualityMap.put(quality.getName(), quality.getId());
            // 构建成色与大类的关联映射
            associationMapping.qualityCategoryMap.put(quality.getId(), quality.getCategoryId().intValue());
        });
        technologies.forEach(technology -> associationMapping.technologyMap.put(technology.getName(), technology.getId()));
        jewelries.forEach(jewelry -> associationMapping.jewelryMap.put(jewelry.getName(), jewelry.getId()));

        // 获取自定义字段
        List<GoodsColumnEntity> customColumns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsColumnEntity::getCategory).eq(GoodsColumnCategoryEnum.CUSTOM.getValue())
        );

        // 构建自定义字段映射
        customColumns.forEach(column -> associationMapping.customColumnMap.put(column.getSign(), column));
    }

    @Override
    public void exportTemplate(GoodsIncomeTemplateExportDTO dto) {
        // 查询模板详情
        List<GoodsIncomeTemplateDetailEntity> details = goodsIncomeTemplateDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(dto.getTemplateId())
                .and(GoodsIncomeTemplateDetailEntity::getSign).ne("image")
                .and(GoodsIncomeTemplateDetailEntity::getEnabled).eq(1)
                .orderBy(GoodsIncomeTemplateDetailEntity::getId).asc()
        );

        // 过滤掉图片类型的自定义字段
        details = details.stream()
            .filter(detail -> {
                // 通过字段的sign获取字段信息
                GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(detail.getSign());
                // 如果是图片类型(type=6)，则过滤掉
                return column == null || !column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue());
            })
            .collect(Collectors.toList());

        // 查询字段列表
        List<GoodsColumnEntity> columns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId().intValue())
        );
        // 字段 map <sign, name>
        Map<String, String> columnMap = columns.stream()
            .collect(Collectors.toMap(GoodsColumnEntity::getSign, GoodsColumnEntity::getName, (v1, v2) -> v1));

        // 获取 income_create 表头配置的排序
        TableHeading tableHeading = headingSignsService.getTableHeading("income_create", dto.getTemplateId().intValue());
        List<HeadingColumns> headingColumns = tableHeading.getColumns();

        // 创建表头排序映射 <sign, index>
        Map<String, Integer> headingOrderMap = new HashMap<>();
        for (int i = 0; i < headingColumns.size(); i++) {
            String prop = headingColumns.get(i).getProp();
            // 驼峰转下划线
            String sign = StrUtil.toUnderlineCase(prop);
            if (sign.equals("category_name")) {
                sign = "category_id";
            }
            headingOrderMap.put(sign, i);
        }

        // 按照 income_create 表头配置的排序对模板详情进行排序
        details.sort((a, b) -> {
            Integer orderA = headingOrderMap.get(a.getSign());
            Integer orderB = headingOrderMap.get(b.getSign());
            // 如果在表头配置中找不到，则放到最后
            if (orderA == null && orderB == null) return 0;
            if (orderA == null) return 1;
            if (orderB == null) return -1;
            return orderA.compareTo(orderB);
        });

        // 获取表头
        List<String> headers = details.stream()
            .map(detail -> columnMap.get(detail.getSign()))
            .collect(Collectors.toList());

        // 准备表头注释
        Map<Integer, String> headerComments = new HashMap<>();
        for (int i = 0; i < details.size(); i++) {
            GoodsIncomeTemplateDetailEntity detail = details.get(i);
            GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(detail.getSign());

            StringBuilder commentText = new StringBuilder();

            // 必填字段注释
            if (Integer.valueOf(1).equals(detail.getRequiredFlag())) {
                commentText.append("必填\n");
            }

            // type 5 is select
            if (column != null && column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                String optionsStr = column.getOptions();
                if (StrUtil.isNotBlank(optionsStr) && JSONUtil.isTypeJSONArray(optionsStr)) {
                    JSONArray optionArray = JSONUtil.parseArray(optionsStr);
                    List<String> labels = optionArray.stream()
                            .filter(JSONObject.class::isInstance)
                            .map(o -> ((JSONObject) o).getStr("label"))
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(labels)) {
                        if (Integer.valueOf(1).equals(column.getIsMultiple())) {
                            commentText.append("可多选，请用中文顿号'、'分隔。\n");
                        } else {
                            commentText.append("请从下列选项中选择一项填写。\n");
                        }
                        commentText.append("有效选项: ").append(String.join("、", labels));
                    }
                }
            }

            // 如果有注释内容，则添加到注释映射中
            if (commentText.length() > 0) {
                headerComments.put(i, commentText.toString());
            }
        }

        // 获取响应对象
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("货品入库模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");

            // 创建空数据列表
            List<Map<String, String>> dataList = new ArrayList<>();

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                .head(headers.stream().map(header -> List.of(header)).collect(Collectors.toList()))
                .sheet("货品入库模板")
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15)) // 设置固定列宽为15个字符
                .registerWriteHandler(new CellWriteHandler() {
                    @Override
                    public void afterCellDispose(CellWriteHandlerContext context) {
                        if (Boolean.TRUE.equals(context.getHead()) && headerComments.containsKey(context.getColumnIndex())) {
                            Sheet sheet = context.getWriteSheetHolder().getSheet();
                            Drawing<?> drawing = sheet.getDrawingPatriarch();
                            if (drawing == null) {
                                drawing = sheet.createDrawingPatriarch();
                            }
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0,
                                    context.getColumnIndex(), context.getRowIndex(), context.getColumnIndex() + 4,
                                    context.getRowIndex() + 5));
                            comment.setString(new XSSFRichTextString(headerComments.get(context.getColumnIndex())));
                            context.getCell().setCellComment(comment);
                        }
                    }
                })
                .doWrite(dataList);
        } catch (IOException e) {
            throw new RuntimeException("导出模板失败", e);
        }
    }

    /**
     * 获取模板字段映射
     *
     * @param templateId 模板ID
     * @return 包含字段名称到字段标识的映射、字段标识到是否必填的映射、字段标识到默认值的映射等
     */
    private Map<String, Object> getTemplateColumnMap(Long templateId) {
        // 查询模板字段
        List<GoodsIncomeTemplateDetailEntity> templateDetails = goodsIncomeTemplateDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(templateId)
        );

        if (CollUtil.isEmpty(templateDetails)) {
            CommonUtils.abort("模板未配置任何字段");
        }

        // 获取所有字段标识
        Set<String> signs = templateDetails.stream()
            .map(GoodsIncomeTemplateDetailEntity::getSign)
            .collect(Collectors.toSet());

        // 查询字段信息（包含图片字段）
        List<GoodsColumnEntity> columns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getSign).in(signs)
        );

        // 构建字段名称到字段标识的映射
        Map<String, String> columnMap = columns.stream()
            .collect(Collectors.toMap(
                GoodsColumnEntity::getName,
                GoodsColumnEntity::getSign,
                (v1, v2) -> v1
            ));

        // 构建字段标识到是否必填的映射
        Map<String, Integer> requiredMap = templateDetails.stream()
            .collect(Collectors.toMap(
                GoodsIncomeTemplateDetailEntity::getSign,
                GoodsIncomeTemplateDetailEntity::getRequiredFlag,
                (v1, v2) -> v1
            ));

        // 构建字段标识到默认值的映射
        Map<String, String> defaultValueMap = templateDetails.stream()
            .collect(Collectors.toMap(
                GoodsIncomeTemplateDetailEntity::getSign,
                detail -> detail.getDefaultValue() != null ? detail.getDefaultValue() : "",
                (v1, v2) -> v1
            ));

        // 构建字段标识到图片ID的映射（用于图片字段的默认值）
        Map<String, Long> imageIdMap = templateDetails.stream()
            .filter(detail -> detail.getImageId() != null && detail.getImageId() > 0)
            .collect(Collectors.toMap(
                GoodsIncomeTemplateDetailEntity::getSign,
                GoodsIncomeTemplateDetailEntity::getImageId,
                (v1, v2) -> v1
            ));

        // 构建字段标识到字段名称的映射（用于错误消息）
        Map<String, String> signToNameMap = columns.stream()
            .collect(Collectors.toMap(
                GoodsColumnEntity::getSign,
                GoodsColumnEntity::getName,
                (v1, v2) -> v1
            ));

        // 构建模板详情映射（字段标识到模板详情对象）
        Map<String, GoodsIncomeTemplateDetailEntity> templateDetailMap = templateDetails.stream()
            .collect(Collectors.toMap(
                GoodsIncomeTemplateDetailEntity::getSign,
                detail -> detail,
                (v1, v2) -> v1
            ));

        Map<String, Object> result = new HashMap<>();
        result.put("columnMap", columnMap);
        result.put("requiredMap", requiredMap);
        result.put("defaultValueMap", defaultValueMap);
        result.put("imageIdMap", imageIdMap);
        result.put("signToNameMap", signToNameMap);
        result.put("templateDetailMap", templateDetailMap);
        return result;
    }





    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GoodsIncomeDetailVO> parseTemplate(MultipartFile file, Long templateId, Integer merchantId) {
        // 1. 验证文件
        if (file == null || file.isEmpty()) {
            CommonUtils.abort("文件不能为空");
        }

        // 2. 验证模板ID
        if (templateId == null) {
            CommonUtils.abort("模板ID不能为空");
        }

        // 3. 获取模板信息，校验模板的大类ID
        GoodsIncomeTemplateEntity template = goodsIncomeTemplateMapper.selectOneById(templateId);
        CommonUtils.abortIf(template == null, "模板不存在");
        Integer templateCategoryId = template.getCategoryId();

        // 4. 初始化关联数据映射
        initAssociationMappings();

        // 4. 读取Excel文件
        ExcelReader reader;
        try {
            reader = ExcelUtil.getReader(file.getInputStream());
        } catch (IOException e) {
            CommonUtils.abort("读取文件失败：" + e.getMessage());
            return null;
        }

        // 5. 获取模板字段映射
        Map<String, Object> templateMaps = getTemplateColumnMap(templateId);
        @SuppressWarnings("unchecked")
        Map<String, String> columnMap = (Map<String, String>) templateMaps.get("columnMap");
        @SuppressWarnings("unchecked")
        Map<String, Integer> requiredMap = (Map<String, Integer>) templateMaps.get("requiredMap");
        @SuppressWarnings("unchecked")
        Map<String, String> defaultValueMap = (Map<String, String>) templateMaps.get("defaultValueMap");
        @SuppressWarnings("unchecked")
        Map<String, Long> imageIdMap = (Map<String, Long>) templateMaps.get("imageIdMap");
        @SuppressWarnings("unchecked")
        Map<String, String> signToNameMap = (Map<String, String>) templateMaps.get("signToNameMap");
        @SuppressWarnings("unchecked")
        Map<String, GoodsIncomeTemplateDetailEntity> templateDetailMap = (Map<String, GoodsIncomeTemplateDetailEntity>) templateMaps.get("templateDetailMap");

        // 6. 读取所有数据（跳过表头校验，支持任意字段组合）
        List<Map<String, Object>> rows = reader.readAll();

        // 8. 验证数据
        if (CollUtil.isEmpty(rows)) {
            CommonUtils.abort("Excel文件内容为空");
        }

        // 9. 转换数据
        List<GoodsIncomeDetailVO> result = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> row = rows.get(i);
            GoodsIncomeDetailVO vo = new GoodsIncomeDetailVO();

            try {
                // 遍历模板中的所有字段（不仅仅是Excel中存在的字段）
                for (String sign : templateDetailMap.keySet()) {
                    // 获取字段名称
                    String fieldName = signToNameMap.get(sign);
                    Object excelValue = null;

                    // 如果Excel中有这个字段，则从Excel中获取值
                    if (fieldName != null && columnMap.containsKey(fieldName)) {
                        excelValue = row.get(fieldName);
                    }

                    // 如果Excel中没有值或值为空，则使用默认值
                    Object finalValue = excelValue;
                    if (finalValue == null || (finalValue instanceof String && StrUtil.isBlank((String) finalValue))) {
                        String defaultValue = defaultValueMap.get(sign);
                        if (StrUtil.isNotBlank(defaultValue)) {
                            // 将默认值转换为对应的名称（模仿Excel导入的数据格式）
                            finalValue = convertDefaultValueToName(sign, defaultValue, templateDetailMap);
                        }
                    }

                    // 根据sign设置值到VO对象
                    setFieldValue(vo, sign, finalValue, i + 2, requiredMap, signToNameMap, templateDetailMap);
                }

                // 设置模板的大类ID到所有行
                vo.setCategoryId(templateCategoryId);

                // 校验关联关系
                validateAssociations(vo, i + 2, templateCategoryId, merchantId);

                // 自动计算成本价和重量
                calculatePriceAndWeight(vo, i + 2);

                // 确保图片字段始终存在
                if (vo.getImage() == null) {
                    vo.setImage(new ArrayList<>());
                }



                result.add(vo);
            } catch (Exception e) {
                CommonUtils.abort("第" + (i + 2) + "行数据解析失败：" + e.getMessage());
            }
        }

        // 检查模板数据中是否存在重复的条码
        checkDuplicateGoodsSn(result);

        // 批量检查条码是否已存在
        checkGoodsSnExistsBatch(result);

        return result;
    }

    /**
     * 将默认值转换为对应的名称（模仿Excel导入的数据格式）
     *
     * @param sign 字段标识
     * @param defaultValue 默认值（通常是ID）
     * @param templateDetailMap 模板详情映射
     * @return 转换后的值
     */
    private Object convertDefaultValueToName(String sign, String defaultValue, Map<String, GoodsIncomeTemplateDetailEntity> templateDetailMap) {
        if (StrUtil.isBlank(defaultValue)) {
            return defaultValue;
        }

        // 处理自定义字段
        GoodsColumnEntity customColumn = associationMapping.customColumnMap.get(sign);
        if (customColumn != null) {
            // 如果是下拉选择类型，需要将value转换为label
            if (customColumn.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                return convertSelectDefaultValue(customColumn, defaultValue);
            }
            // 如果是图片类型，需要特殊处理
            if (customColumn.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
                // 从模板详情中获取图片ID
                GoodsIncomeTemplateDetailEntity templateDetail = templateDetailMap.get(sign);
                Long imageId = (templateDetail != null && templateDetail.getImageId() != null) ? templateDetail.getImageId() : 0L;
                return convertImageDefaultValue(defaultValue, imageId);
            }
            // 其他自定义字段类型直接返回默认值
            return defaultValue;
        }

        // 处理系统字段：目前默认值全部按原样返回，仅对 cost_price 做 0->null 的特殊处理
        if ("cost_price".equals(sign)) {
            return "0".equals(defaultValue) ? null : defaultValue;
        }
        return defaultValue;
    }

    /**
     * 转换图片字段的默认值
     *
     * @param defaultValue 默认值（图片链接URL）
     * @param imageId 图片ID
     * @return 转换后的值（图片对象数组）
     */
    private Object convertImageDefaultValue(String defaultValue, Long imageId) {
        if (StrUtil.isBlank(defaultValue) && (imageId == null || imageId <= 0)) {
            return new ArrayList<>();
        }

        // 构建图片对象
        Map<String, Object> image = new HashMap<>();
        image.put("id", imageId != null ? imageId : 0); // 使用实际的图片ID
        image.put("imageId", imageId != null ? imageId : 0); // 使用实际的图片ID
        image.put("url", StrUtil.isNotBlank(defaultValue) ? defaultValue : ""); // 使用传入的图片链接

        return List.of(image);
    }

    /**
     * 转换下拉选择字段的默认值
     *
     * @param customColumn 自定义字段信息
     * @param defaultValue 默认值
     * @return 转换后的值
     */
    private Object convertSelectDefaultValue(GoodsColumnEntity customColumn, String defaultValue) {
        String optionsStr = customColumn.getOptions();
        if (StrUtil.isBlank(optionsStr) || !JSONUtil.isTypeJSONArray(optionsStr)) {
            return defaultValue;
        }

        JSONArray optionArray = JSONUtil.parseArray(optionsStr);
        Map<String, String> valueToLabelMap = optionArray.stream()
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .collect(Collectors.toMap(o -> o.getStr("value"), o -> o.getStr("label"),
                        (v1, v2) -> v1));

        if (Integer.valueOf(1).equals(customColumn.getIsMultiple())) {
            // 多选字段：默认值可能是JSON数组格式
            try {
                if (JSONUtil.isTypeJSONArray(defaultValue)) {
                    // 如果是JSON数组，解析并转换为标签
                    JSONArray defaultArray = JSONUtil.parseArray(defaultValue);
                    List<String> labels = defaultArray.stream()
                            .map(Object::toString)
                            .map(valueToLabelMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    // 返回用顿号分隔的标签字符串
                    return String.join("、", labels);
                } else {
                    // 单个值，直接转换
                    String label = valueToLabelMap.get(defaultValue);
                    return label != null ? label : defaultValue;
                }
            } catch (Exception e) {
                // 解析失败，尝试作为单个值处理
                String label = valueToLabelMap.get(defaultValue);
                return label != null ? label : defaultValue;
            }
        } else {
            // 单选字段：直接转换
            String label = valueToLabelMap.get(defaultValue);
            return label != null ? label : defaultValue;
        }
    }

    /**
     * 设置字段值
     *
     * @param vo 目标对象
     * @param sign 字段标识
     * @param value 字段值（可能来自Excel或默认值）
     * @param rowNum 行号
     * @param requiredMap 字段标识到是否必填的映射
     * @param signToNameMap 字段标识到字段名称的映射
     * @param templateDetailMap 模板详情映射
     */
    private void setFieldValue(GoodsIncomeDetailVO vo, String sign, Object value, int rowNum,
            Map<String, Integer> requiredMap, Map<String, String> signToNameMap,
            Map<String, GoodsIncomeTemplateDetailEntity> templateDetailMap) {
        String strValue = value == null ? "" : value.toString().trim();
        String rowPrefix = "";

        // 必填字段校验（只有在值为空且没有默认值的情况下才报错）
        // if (StrUtil.isBlank(strValue) && Integer.valueOf(1).equals(requiredMap.get(sign))) {
        //     String fieldName = signToNameMap.get(sign);
        //     CommonUtils.abort(rowPrefix + fieldName + "不能为空");
        //     return;
        // }

        // 如果没有值，对于字符串字段返回空字符串而不是null
        if (StrUtil.isBlank(strValue)) {
            strValue = "";
        }

        // 处理自定义字段
        GoodsColumnEntity customColumn = associationMapping.customColumnMap.get(sign);
        if (customColumn != null) {
            Object finalValue = strValue;

            // 如果是图片类型，需要特殊处理
            if (customColumn.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
                // 如果value是从默认值转换来的对象数组，直接使用
                if (value instanceof List) {
                    finalValue = value;
                } else if (StrUtil.isBlank(strValue)) {
                    // 如果没有值，返回空数组
                    finalValue = new ArrayList<>();
                } else {
                    // 否则使用字符串值
                    finalValue = strValue;
                }
            }
            // 如果是下拉选择类型(type=5)，需要校验选项并转换值为key
            else if (customColumn.getType().equals(GoodsColumnTypeEnum.SELECT.getValue()) && StrUtil.isNotBlank(strValue)) {
                validateSelectOptions(customColumn, strValue, rowPrefix, signToNameMap.get(sign));

                String optionsStr = customColumn.getOptions();
                if (StrUtil.isNotBlank(optionsStr) && JSONUtil.isTypeJSONArray(optionsStr)) {
                    JSONArray optionArray = JSONUtil.parseArray(optionsStr);
                    Map<String, String> labelToValueMap = optionArray.stream()
                            .filter(JSONObject.class::isInstance)
                            .map(JSONObject.class::cast)
                            .collect(Collectors.toMap(o -> o.getStr("label"), o -> o.getStr("value"),
                                    (v1, v2) -> v1));

                    if (Integer.valueOf(1).equals(customColumn.getIsMultiple())) {
                        // 多选，将顿号分隔的标签转换为 JSON 数组格式的字符串
                        List<String> keys = StrUtil.split(strValue, '、', true, true).stream()
                                .map(labelToValueMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        finalValue = keys;
                    } else {
                        // 单选
                        finalValue = labelToValueMap.get(strValue);
                    }
                }
            }

            CustomColumnItemDTO columnItem = new CustomColumnItemDTO();
            columnItem.setColumnId(customColumn.getId().intValue());
            columnItem.setColumnSign(customColumn.getSign());
            columnItem.setType(customColumn.getType());
            columnItem.setSecretLevel(customColumn.getSecretLevel());
            columnItem.setIsMultiple(customColumn.getIsMultiple());
            columnItem.setNumberPrecision(customColumn.getNumberPrecision());
            columnItem.setValue(finalValue);

            // 如果是图片字段且有默认图片ID，需要设置imageId
            if (customColumn.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
                // 从模板详情中获取图片ID
                GoodsIncomeTemplateDetailEntity templateDetail = templateDetailMap.get(sign);
                if (templateDetail != null && templateDetail.getImageId() != null && templateDetail.getImageId() > 0) {
                    columnItem.setImageId(templateDetail.getImageId().intValue());
                }
            }

            // 添加到自定义列列表
            if (vo.getCustomColumn() == null) {
                vo.setCustomColumn(new ArrayList<>());
            }
            vo.getCustomColumn().add(columnItem);
            return;
        }

        switch (sign) {
            case "goods_id":
                // 商品ID，默认为0
                vo.setGoodsId(0);
                break;
            case "category_id":
                // 大类ID统一来自模板，不从Excel中读取
                // 跳过处理，在外层统一设置
                break;
            case "counter_id":
                // 柜台ID：既支持直接填写ID，也支持填写名称。优先解析为数字ID以避免重名导致误匹配。
                vo.setCounterId(resolveCounterId(strValue, rowPrefix));
                break;
            case "subclass_id":
                // 子类ID：支持ID或名称，优先解析数字ID
                vo.setSubclassId(resolveAssociationId(strValue, rowPrefix, "小类", associationMapping.subclassMap));
                break;
            case "brand_id":
                // 品牌ID：支持ID或名称，优先解析数字ID
                vo.setBrandId(resolveAssociationId(strValue, rowPrefix, "品牌", associationMapping.brandMap));
                break;
            case "style_id":
                // 款式ID：支持ID或名称，优先解析数字ID
                vo.setStyleId(resolveAssociationId(strValue, rowPrefix, "款式", associationMapping.styleMap));
                break;
            case "quality_id":
                // 成色ID：支持ID或名称，优先解析数字ID
                vo.setQualityId(resolveAssociationId(strValue, rowPrefix, "成色", associationMapping.qualityMap));
                break;
            case "technology_id":
                // 工艺ID：支持ID或名称，优先解析数字ID
                vo.setTechnologyId(resolveAssociationId(strValue, rowPrefix, "工艺", associationMapping.technologyMap));
                break;
            case "main_stone_id":
                // 主石ID：支持ID或名称，优先解析数字ID
                vo.setMainStoneId(resolveAssociationId(strValue, rowPrefix, "主石", associationMapping.jewelryMap));
                break;
            case "sub_stone_id":
                // 辅石ID：支持ID或名称，优先解析数字ID
                vo.setSubStoneId(resolveAssociationId(strValue, rowPrefix, "辅石", associationMapping.jewelryMap));
                break;
            case "goods_sn":
                vo.setGoodsSn(strValue);
                break;
            case "name":
                // 商品名称
                vo.setName(StrUtil.isBlank(strValue) ? "" : strValue);
                break;
            case "sales_type":
                // 销售方式
                vo.setSalesType(validateAndConvertSalesType(strValue, rowPrefix, "销售方式"));
                break;
            case "batch_no":
                // 批次号
                vo.setBatchNo(StrUtil.isBlank(strValue) ? "" : strValue);
                break;
            case "cert_no":
                // 证书号
                vo.setCertNo(StrUtil.isBlank(strValue) ? "" : strValue);
                break;
            case "remark":
                // 备注
                vo.setRemark(StrUtil.isBlank(strValue) ? "" : strValue);
                break;
            case "weight":
                // 重量
                vo.setWeight(getBigDecimalValue(value));
                break;
            case "net_gold_weight":
                // 净金重
                vo.setNetGoldWeight(getBigDecimalValue(value));
                break;
            case "net_silver_weight":
                // 净银重
                vo.setNetSilverWeight(getBigDecimalValue(value));
                break;
            case "main_stone_count":
                // 主石数量
                vo.setMainStoneCount(getIntegerValue(value));
                break;
            case "main_stone_weight":
                // 主石重量
                vo.setMainStoneWeight(getBigDecimalValue(value));
                break;
            case "sub_stone_count":
                // 辅石数量
                vo.setSubStoneCount(getIntegerValue(value));
                break;
            case "sub_stone_weight":
                // 辅石重量
                vo.setSubStoneWeight(getBigDecimalValue(value));
                break;
            case "circle_size":
                // 圈口
                vo.setCircleSize(StrUtil.isBlank(strValue) ? "" : strValue);
                break;
            case "cost_price":
                // 成本价 - 输入为元
                vo.setCostPrice(getBigDecimalValue(value));
                break;
            case "gold_price":
                // 金价 - 输入为元
                vo.setGoldPrice(getBigDecimalValue(value));
                break;
            case "silver_price":
                // 银价 - 输入为元
                vo.setSilverPrice(getBigDecimalValue(value));
                break;
            case "work_price":
                // 工费 - 输入为元
                vo.setWorkPrice(getBigDecimalValue(value));
                break;
            case "cert_price":
                // 证书费 - 输入为元
                vo.setCertPrice(getBigDecimalValue(value));
                break;
            case "sale_work_price":
                // 销售工费 - 输入为元
                vo.setSaleWorkPrice(getBigDecimalValue(value));
                break;
            case "tag_price":
                // 标签价 - 输入为元
                vo.setTagPrice(getBigDecimalValue(value));
                break;
            case "num":
                // 数量
                vo.setNum(getIntegerValue(value));
                break;
            case "work_price_type":
                // 进工费计价方式
                vo.setWorkPriceType(validateAndConvertSalesType(strValue, rowPrefix, "进工费计价方式"));
                break;
            case "sale_work_price_type":
                // 销工费计价方式
                vo.setSaleWorkPriceType(validateAndConvertSalesType(strValue, rowPrefix, "销工费计价方式"));
                break;
            case "image":
                // 图片字段处理 - 只处理字符串类型的图片链接
                if (StrUtil.isNotBlank(strValue)) {
                    // 如果是字符串类型的图片链接，需要转换为FileItemDTO
                    com.xc.boot.common.base.FileItemDTO fileItem = new com.xc.boot.common.base.FileItemDTO();
                    fileItem.setUrl(strValue);
                    // 从模板详情中获取图片ID
                    GoodsIncomeTemplateDetailEntity templateDetail = templateDetailMap.get(sign);
                    if (templateDetail != null && templateDetail.getImageId() != null && templateDetail.getImageId() > 0) {
                        fileItem.setId(templateDetail.getImageId());
                        fileItem.setImageId(templateDetail.getImageId());
                    }
                    List<com.xc.boot.common.base.FileItemDTO> fileItems = List.of(fileItem);
                    vo.setImage(fileItems);
                } else {
                    // 如果没有图片或为空，设置空列表
                    vo.setImage(new ArrayList<>());
                }
                break;
        }
    }

    /**
     * 校验关联关系
     *
     * @param vo 入库单明细VO
     * @param rowNum 行号
     * @param templateCategoryId 模板的大类ID
     * @param merchantId 门店ID
     */
    private void validateAssociations(GoodsIncomeDetailVO vo, int rowNum, Integer templateCategoryId, Integer merchantId) {
        String rowPrefix = "";

        // 校验柜台与门店的关联关系
        if (vo.getCounterId() != null && merchantId != null) {
            Integer expectedMerchantId = associationMapping.counterMerchantMap.get(vo.getCounterId().longValue());
            if (expectedMerchantId != null && !expectedMerchantId.equals(merchantId)) {
                CommonUtils.abort(rowPrefix + "柜台与门店不匹配，该柜台不属于所选择的门店");
            }
        }

        // 小类校验已移除，因为小类不再关联大类

        // 校验成色与大类的关联关系
        if (vo.getQualityId() != null && templateCategoryId != null) {
            Integer expectedCategoryId = associationMapping.qualityCategoryMap.get(vo.getQualityId().longValue());
            if (expectedCategoryId != null && !expectedCategoryId.equals(templateCategoryId)) {
                CommonUtils.abort(rowPrefix + "成色与模板设定的大类不匹配，该成色不属于模板的大类");
            }
        }
    }

    /**
     * 校验下拉选择字段的选项
     *
     * @param customColumn 自定义字段信息
     * @param value 填写的值
     * @param rowPrefix 行前缀
     * @param fieldName 字段名称
     */
    private void validateSelectOptions(GoodsColumnEntity customColumn, String value, String rowPrefix, String fieldName) {
        String options = customColumn.getOptions();
        if (StrUtil.isBlank(options) || !JSONUtil.isTypeJSONArray(options)) {
            return; // 如果没有配置选项或格式不正确，则不校验
        }

        JSONArray optionArray = JSONUtil.parseArray(options);
        Set<String> validLabels = optionArray.stream()
                .filter(JSONObject.class::isInstance)
                .map(o -> ((JSONObject) o).getStr("label"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (validLabels.isEmpty()) {
            return; // 没有可用的选项
        }

        // 检查是否为多选
        if (Integer.valueOf(1).equals(customColumn.getIsMultiple())) {
            // 多选，使用中文顿号分割
            List<String> selectedValues = StrUtil.split(value, '、', true, true);
            List<String> invalidValues = selectedValues.stream()
                    .filter(v -> !validLabels.contains(v))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(invalidValues)) {
                CommonUtils.abort(rowPrefix + fieldName + " 的值中包含无效选项: [" +
                        String.join("、", invalidValues) + "]。有效选项为：" +
                        String.join("、", validLabels));
            }
        } else {
            // 单选
            if (!validLabels.contains(value)) {
                CommonUtils.abort(rowPrefix + fieldName + " 的值 [" + value + "] 无效。有效选项为：" +
                        String.join("、", validLabels));
            }
        }
    }

    /**
     * 获取整数值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        String strValue = value.toString().trim();
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        // 清理字符串，移除非数字字符（保留数字、负号）
        strValue = strValue.replaceAll("[^0-9\\-+]", "");

        // 再次检查是否为空
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        try {
            return Integer.parseInt(strValue);
        } catch (NumberFormatException e) {
            // 如果仍然无法解析，返回null而不是抛出异常
            log.warn("无法解析整数值: {}", value);
            return null;
        }
    }

    /**
     * 获取长整数值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        String strValue = value.toString().trim();
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        // 清理字符串，移除非数字字符（保留数字、负号）
        strValue = strValue.replaceAll("[^0-9\\-+]", "");

        // 再次检查是否为空
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        try {
            return Long.parseLong(strValue);
        } catch (NumberFormatException e) {
            // 如果仍然无法解析，返回null而不是抛出异常
            log.warn("无法解析长整数值: {}", value);
            return null;
        }
    }

    /**
     * 解析关联ID（支持ID或名称输入）
     *
     * @param strValue 输入值
     * @param rowPrefix 行前缀（用于错误提示）
     * @param fieldName 字段名称（用于错误提示）
     * @param nameToIdMap 名称到ID的映射
     * @return 解析后的ID值
     */
    private Integer resolveAssociationId(String strValue, String rowPrefix, String fieldName,
                                       Map<String, Long> nameToIdMap) {
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        Long parsedId = getLongValue(strValue);
        if (parsedId != null && parsedId > 0) {
            // 通过检查 nameToIdMap 的值来验证ID是否存在
            boolean exists = nameToIdMap.containsValue(parsedId);
            CommonUtils.abortIf(!exists, rowPrefix + "无法识别的" + fieldName + "ID: " + parsedId);
            return parsedId.intValue();
        } else {
            Long id = nameToIdMap.get(strValue);
            if (id == null) {
                CommonUtils.abort(rowPrefix + "无法识别的" + fieldName + ": " + strValue);
                return null; // 这行不会执行，但避免IDE警告
            }
            return id.intValue();
        }
    }

    /**
     * 解析柜台ID（特殊处理，包含门店关联校验）
     *
     * @param strValue 输入值
     * @param rowPrefix 行前缀（用于错误提示）
     * @return 解析后的柜台ID
     */
    private Integer resolveCounterId(String strValue, String rowPrefix) {
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        Long parsedId = getLongValue(strValue);
        if (parsedId != null && parsedId > 0) {
            // 校验ID是否为已知柜台（通过已建立的 柜台ID->门店ID 映射做存在性校验）
            Integer existMerchantId = associationMapping.counterMerchantMap.get(parsedId);
            CommonUtils.abortIf(existMerchantId == null, rowPrefix + "无法识别的柜台ID: " + parsedId);
            return parsedId.intValue();
        } else {
            Long counterId = associationMapping.counterMap.get(strValue);
            if (counterId == null) {
                CommonUtils.abort(rowPrefix + "无法识别的柜台: " + strValue);
                return null; // 这行不会执行，但避免IDE警告
            }
            return counterId.intValue();
        }
    }

    /**
     * 校验并转换销售方式/工费计价方式
     *
     * @param strValue 输入值
     * @param rowPrefix 行前缀
     * @param fieldName 字段名称
     * @return 转换后的值
     */
    private String validateAndConvertSalesType(String strValue, String rowPrefix, String fieldName) {
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        // 检查是否为有效的文本标签
        if (SalesTypeEnum.BY_WEIGHT.getLabel().equals(strValue)) {
            return SalesTypeEnum.BY_WEIGHT.getValue().toString();
        } else if (SalesTypeEnum.BY_NUM.getLabel().equals(strValue)) {
            return SalesTypeEnum.BY_NUM.getValue().toString();
        } else if ("1".equals(strValue) || "2".equals(strValue)) {
            // 如果直接是数字，校验是否为有效值
            return strValue;
        } else {
            // 无效值，给出提示
            CommonUtils.abort(rowPrefix + fieldName + "的值 [" + strValue + "] 无效。有效选项为：按重量、按数量");
            return null;
        }
    }

    /**
     * 获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        String strValue = value.toString().trim();
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        // 清理字符串，移除非数字字符（保留数字、小数点、负号、科学计数法）
        strValue = strValue.replaceAll("[^0-9.\\-+eE]", "");

        // 再次检查是否为空
        if (StrUtil.isBlank(strValue)) {
            return null;
        }

        try {
            return new BigDecimal(strValue);
        } catch (NumberFormatException e) {
            // 如果仍然无法解析，返回null而不是抛出异常
            log.warn("无法解析数字值: {}", value);
            return null;
        }
    }

    /**
     * 自动计算成本价和重量
     * 1. 成本价自动计算：有成本单价直接取，无成本单价根据工费计价方式计算
     * 2. 重量自动计算：有重量直接取，无重量根据公式计算
     * 3. 证书费不能大于成本单价
     *
     * @param vo 入库单明细VO
     * @param rowNum 行号
     */
    private void calculatePriceAndWeight(GoodsIncomeDetailVO vo, int rowNum) {
        String rowPrefix = "";

        // 1. 重量自动计算
        calculateWeight(vo);

        // 2. 成本价自动计算
        calculateCostPrice(vo);

        // 3. 校验：证书费不能大于成本单价
        if (vo.getCertPrice() != null && vo.getCostPrice() != null) {
            if (vo.getCertPrice().compareTo(vo.getCostPrice()) > 0) {
                CommonUtils.abort(rowPrefix + "证书费不能大于成本单价");
            }
        }
    }

    /**
     * 重量自动计算
     * 有重量：直接取重量
     * 无重量：重量 = 净金重 + 净银重 + （主石重 * 主石数 * 0.2） + （辅石数 * 辅石重 * 0.2）
     *
     * @param vo 入库单明细VO
     */
    private void calculateWeight(GoodsIncomeDetailVO vo) {
        // 如果已有重量，则不计算
        if (vo.getWeight() != null && vo.getWeight().compareTo(BigDecimal.ZERO) > 0) {
            return;
        }

        // 获取各项重量，空值当作0处理
        BigDecimal netGoldWeight = vo.getNetGoldWeight() != null ? vo.getNetGoldWeight() : BigDecimal.ZERO;
        BigDecimal netSilverWeight = vo.getNetSilverWeight() != null ? vo.getNetSilverWeight() : BigDecimal.ZERO;
        BigDecimal mainStoneWeight = vo.getMainStoneWeight() != null ? vo.getMainStoneWeight() : BigDecimal.ZERO;
        Integer mainStoneCount = vo.getMainStoneCount() != null ? vo.getMainStoneCount() : 0;
        Integer subStoneCount = vo.getSubStoneCount() != null ? vo.getSubStoneCount() : 0;
        BigDecimal subStoneWeight = vo.getSubStoneWeight() != null ? vo.getSubStoneWeight() : BigDecimal.ZERO;

        // 计算重量：净金重 + 净银重 + （主石重 * 主石数 * 0.2） + （辅石数 * 辅石重 * 0.2）
        BigDecimal calculatedWeight = netGoldWeight
                .add(netSilverWeight)
                .add(mainStoneWeight.multiply(new BigDecimal(mainStoneCount)).multiply(new BigDecimal("0.2")))
                .add(new BigDecimal(subStoneCount).multiply(subStoneWeight).multiply(new BigDecimal("0.2")))
                .setScale(3, RoundingMode.HALF_UP); // 重量保留3位小数

        // 设置计算出的重量
        vo.setWeight(calculatedWeight);
    }

    /**
     * 成本价自动计算（每步四舍五入，对齐前端计算方式）
     * 有成本单价：直接取成本单价
     * 无成本单价：
     * - 进工费计价方式按数量：成本单价 = 金进单价 * 净金重 + 银进单价 * 净银重 + 进工费单价 + 证书费
     * - 进工费计价方式按重量：成本单价 = 金进单价 * 净金重 + 银进单价 * 净银重 + 进工费单价 * （净金重 + 净银重） + 证书费
     * 注意：每个分项计算后都立即四舍五入到2位小数，然后相加得到最终成本价
     *
     * @param vo 入库单明细VO
     */
    private void calculateCostPrice(GoodsIncomeDetailVO vo) {
        // 如果已有成本单价，则不计算
        if (vo.getCostPrice() != null) {
            return;
        }

        // 计算各分项金额
        BigDecimal goldAmount = BigDecimal.ZERO;
        BigDecimal silverAmount = BigDecimal.ZERO;
        BigDecimal workAmount = BigDecimal.ZERO;
        BigDecimal certAmount = BigDecimal.ZERO;

        // 金进金额 = 金进单价 × 净金重（每步都四舍五入，对齐前端计算方式）
        if (vo.getGoldPrice() != null && vo.getNetGoldWeight() != null) {
            goldAmount = vo.getGoldPrice().multiply(vo.getNetGoldWeight()).setScale(2, RoundingMode.HALF_UP);
        }

        // 银进金额 = 银进单价 × 净银重（每步都四舍五入，对齐前端计算方式）
        if (vo.getSilverPrice() != null && vo.getNetSilverWeight() != null) {
            silverAmount = vo.getSilverPrice().multiply(vo.getNetSilverWeight()).setScale(2, RoundingMode.HALF_UP);
        }

        // 进工费金额 - 根据计价方式计算（每步都四舍五入，对齐前端计算方式）
        if (vo.getWorkPrice() != null) {
            String workPriceTypeStr = vo.getWorkPriceType();
            if (workPriceTypeStr != null) {
                try {
                    Integer workPriceType = Integer.valueOf(workPriceTypeStr);
                    if (workPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue())) {
                        // 按重量：工费金额 = 工费单价 × (净金重 + 净银重)
                        BigDecimal netGoldWeight = vo.getNetGoldWeight() != null ? vo.getNetGoldWeight() : BigDecimal.ZERO;
                        BigDecimal netSilverWeight = vo.getNetSilverWeight() != null ? vo.getNetSilverWeight() : BigDecimal.ZERO;
                        BigDecimal workWeight = netGoldWeight.add(netSilverWeight);
                        workAmount = vo.getWorkPrice().multiply(workWeight).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        // 按数量（默认）：进工费单价
                        workAmount = vo.getWorkPrice().setScale(2, RoundingMode.HALF_UP);
                    }
                } catch (NumberFormatException e) {
                    // 如果转换失败，默认按数量计价
                    workAmount = vo.getWorkPrice().setScale(2, RoundingMode.HALF_UP);
                }
            } else {
                // 如果工费计价方式为空，默认按数量计价
                workAmount = vo.getWorkPrice().setScale(2, RoundingMode.HALF_UP);
            }
        }

        // 证书费（每步都四舍五入，对齐前端计算方式）
        if (vo.getCertPrice() != null) {
            certAmount = vo.getCertPrice().setScale(2, RoundingMode.HALF_UP);
        }

        // 计算分项金额总和（各分项已四舍五入，直接相加）
        BigDecimal calculatedCostPrice = goldAmount.add(silverAmount).add(workAmount).add(certAmount);

        // 设置计算出的成本单价
        vo.setCostPrice(calculatedCostPrice);
    }

    /**
     * 批量检查条码是否已存在
     *
     * @param voList 入库单明细VO列表
     */
    private void checkGoodsSnExistsBatch(List<GoodsIncomeDetailVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 收集所有非空的货品条码
        List<String> goodsSns = voList.stream()
                .map(GoodsIncomeDetailVO::getGoodsSn)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (goodsSns.isEmpty()) {
            return;
        }

        // 批量查询已存在的货品条码
        List<GoodsEntity> existingGoods = goodsMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsEntity::getGoodsSn).in(goodsSns)
                .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        if (CollUtil.isEmpty(existingGoods)) {
            return;
        }

        // 构建已存在条码的Set，便于快速查找
        Set<String> existingGoodsSnSet = existingGoods.stream()
                .map(GoodsEntity::getGoodsSn)
                .collect(Collectors.toSet());

        // 检查每个VO，找出条码已存在的行
        for (int i = 0; i < voList.size(); i++) {
            GoodsIncomeDetailVO vo = voList.get(i);
            if (StrUtil.isNotBlank(vo.getGoodsSn()) && existingGoodsSnSet.contains(vo.getGoodsSn())) {
                String rowPrefix = ""; // i + 2 是因为Excel从1开始，且第1行是表头
                CommonUtils.abort(rowPrefix + "条码已存在，请手动操作: " + vo.getGoodsSn());
            }
        }
    }

    /**
     * 检查模板数据中是否存在重复的条码
     *
     * @param voList 入库单明细VO列表
     */
    private void checkDuplicateGoodsSn(List<GoodsIncomeDetailVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 用于记录条码第一次出现的行号
        Map<String, Integer> goodsSnRowMap = new HashMap<>();

        // 检查每个VO的条码
        for (int i = 0; i < voList.size(); i++) {
            GoodsIncomeDetailVO vo = voList.get(i);
            String goodsSn = vo.getGoodsSn();

            // 跳过空条码
            if (StrUtil.isBlank(goodsSn)) {
                continue;
            }

            int currentRow = i + 2; // Excel行号，从第2行开始（第1行是表头）

            // 检查是否已经存在相同的条码
            if (goodsSnRowMap.containsKey(goodsSn)) {
                int firstRow = goodsSnRowMap.get(goodsSn);
                CommonUtils.abort("模板中存在重复的条码：" + goodsSn + "，第" + firstRow + "行和第" + currentRow + "行重复");
            }

            // 记录条码第一次出现的行号
            goodsSnRowMap.put(goodsSn, currentRow);
        }
    }


}